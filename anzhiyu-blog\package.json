{"name": "hexo-site", "version": "0.0.0", "private": true, "scripts": {"dev": "hexo s", "build": "hexo clean && hexo g", "deploy": "hexo clean && hexo d -g", "server": "hexo server"}, "hexo": {"version": "7.3.0"}, "dependencies": {"hexo": "^7.3.0", "hexo-bilibili-bangumi": "^2.0.1", "hexo-butterfly-envelope": "^1.0.15", "hexo-generator-archive": "^2.0.0", "hexo-generator-category": "^2.0.0", "hexo-generator-index": "^4.0.0", "hexo-generator-searchdb": "^1.5.0", "hexo-generator-tag": "^2.0.0", "hexo-renderer-ejs": "^2.0.0", "hexo-renderer-marked": "^7.0.0", "hexo-renderer-pug": "^3.0.0", "hexo-renderer-stylus": "^3.0.1", "hexo-server": "^3.0.0", "hexo-theme-landscape": "^1.0.0"}, "devDependencies": {"hexo-deployer-rsync": "^3.0.0", "js-yaml": "^4.1.0"}}