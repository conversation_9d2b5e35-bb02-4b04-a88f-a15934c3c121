# Hexo 博客一键部署配置指南

## 🚀 快速开始

### 1. 服务器准备
```bash
# 在服务器上执行（替换 your-username 为你的用户名）
sudo mkdir -p /var/www/anzhiyu-blog/public
sudo chown -R your-username:your-username /var/www/anzhiyu-blog
```

### 2. SSH 密钥配置
```bash
# 在本地生成部署专用密钥
ssh-keygen -t ed25519 -C "hexo-deploy" -f ~/.ssh/hexo_deploy -N ""

# 将公钥复制到服务器（替换用户名和服务器地址）
ssh-copy-id -i ~/.ssh/hexo_deploy.pub your-username@your-server-ip
```

### 3. SSH 配置文件
在本地 `~/.ssh/config` 文件中添加：
```
Host blog
  HostName your-server-ip-or-domain.com
  User your-username
  Port 22
  IdentityFile ~/.ssh/hexo_deploy
```

### 4. 测试连接
```bash
ssh blog
```

## 📝 使用方法

### 本地预览
```bash
cd anzhiyu-blog
npm run dev
# 访问 http://localhost:4000 预览
```

### 一键部署
```bash
npm run deploy
```

## 🔧 配置说明

当前配置文件 `_config.yml` 中的部署设置：
- **host**: blog (对应 SSH 配置中的 Host)
- **user**: deploy (服务器登录用户)
- **root**: /var/www/anzhiyu-blog/public (服务器目标目录)
- **delete**: true (删除服务器上的旧文件)

## ⚠️ 注意事项

1. **域名配置**: 请将 `_config.yml` 中的 `url` 更新为你的实际域名
2. **服务器路径**: 确保 Nginx 或其他 Web 服务器指向 `/var/www/anzhiyu-blog/public`
3. **权限检查**: 确保部署用户对目标目录有写权限
4. **防火墙**: 确保服务器 22 端口（SSH）可访问

## 🐛 常见问题

### 权限错误
```bash
# 在服务器上修复权限
sudo chown -R your-username:your-username /var/www/anzhiyu-blog
chmod -R 755 /var/www/anzhiyu-blog
```

### 连接失败
- 检查 SSH 配置文件路径和内容
- 确认服务器 IP/域名和端口正确
- 测试 SSH 连接：`ssh blog`

### 部署后页面空白
- 检查 `_config.yml` 中的 `url` 和 `root` 配置
- 确认 Web 服务器配置正确
